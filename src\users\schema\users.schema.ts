import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';

@Schema({_id:false})
export class HealthAssessment {
  @Prop({ type: String })
  ageRange?: string;

  @Prop({ type: Number })
  height?: number;

  @Prop({ type: Number })
  weight?: number;

  @Prop({ type: Number })
  bmi?: number;
  
  @Prop({ type: Number }) 
  bodyFatPercentage?: number;

  @Prop({ type: [String] })
  conditionsToManage?: string[];
}

@Schema({_id:false})
export class DiabetesInfo {
  @Prop({ type: String }) 
  diabetesType?: string; 

  @Prop({ type: String }) 
  diabetesDiagnosedSince?: string; 

  @Prop({ type: Boolean })
  takesMedication?: boolean;

  @Prop({ type: [String] })
  medications?: string[];

  @Prop({ type: Number })
  recentHbA1c?: number;

  @Prop({ type: Boolean, default: false })
  rememberHbA1c?: boolean;

  @Prop({ type: String }) 
  lastTestDuration?: string;

  @Prop({ type: Number })
  recentFastingGlucose?: number;

  @Prop({ type: Number, min: 1, max: 10 })
  diabetesMotivationLevel?: number;
}

@Schema({_id:false})
export class BloodPressureInfo {
  @Prop({ type: Boolean })
  hyperTension?: boolean;

  @Prop({ type: Number })
  diagonisedYear?: number;

  @Prop({ type: Number })
  bpSystolic?: number;

  @Prop({ type: Number })
  bpDiastolic?: number;

  @Prop({ type: Number })
  heartRate?: number;

  @Prop({ type: Boolean, default: false })
  rememberBp?: boolean;

}

@Schema({_id:false})
export class MeasurementReminders {
    @Prop({ type: Boolean, default: false })
    bpMeasurementReminders?: boolean;

    @Prop({ type: Boolean,default:false })
    bloodSugarMeasurementReminders?: boolean;
}

@Schema({_id:false})
export class MentalHealthInfo {
  @Prop({ type: [String] })
  comorbidities?: string[];

  @Prop({ type: String })
  anxietyLevel?: string;

  @Prop({ type: [String] })
  commonEmotions?: string[];
}

@Schema({_id:false})
export class SleepInfo {
  @Prop({ type: String })
  sleepQualityRating?: string;

  @Prop({ type: String })
  troubleFallingAsleep?: string;

  @Prop({ type: String })
  sleepCondition?: string;
}

@Schema({_id:false})
export class FitnessInfo {
  @Prop({ type: String })
  fitnessLevel?: string; 

  @Prop({ type: Boolean, default: false })
  motivationInsightsAccepted?: boolean;
}

@Schema({ timestamps: true })
export class Users {
  @Prop({ required: true })
  firstName: string;

  @Prop({ required: true })
  lastName: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  password: string;

  @Prop({ required: true })
  mobileNumber: string;

  @Prop({ required: true })
  gender: string;

  @Prop({ required: true })
  dob: string;

  @Prop({ required: false, default: false })
  isVerified: boolean;

  @Prop({ type: HealthAssessment })
  healthAssessment?: HealthAssessment;

  @Prop({ type: DiabetesInfo })
  diabetesInfo?: DiabetesInfo;

  @Prop({ type: BloodPressureInfo })
  bloodPressureInfo?: BloodPressureInfo;

  @Prop({ type: MentalHealthInfo })
  mentalHealthInfo?: MentalHealthInfo;

  @Prop({ type: SleepInfo })
  sleepInfo?: SleepInfo;

  @Prop({ type: FitnessInfo })
  fitnessInfo?: FitnessInfo;

  @Prop({ type: MeasurementReminders })
  Reminders?: MeasurementReminders;
}

export const UsersSchema = SchemaFactory.createForClass(Users);
