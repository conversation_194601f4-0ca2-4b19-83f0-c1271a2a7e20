import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "class-validator";
import { ApiProperty } from '@nestjs/swagger';

export class signInDto{
    @ApiProperty({
        description: 'User\'s email address for authentication',
        example: '<EMAIL>',
        format: 'email',
    })
    @IsString()
    @IsEmail()
    @IsNotEmpty()
    email:string;

    @ApiProperty({
        description: 'User\'s password for authentication',
        example: 'SecurePassword123!',
        minLength: 6,
        format: 'password',
    })
    @IsString()
    @IsNotEmpty()
    @MinLength(6)
    password:string;
}