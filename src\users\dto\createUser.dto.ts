import { IsEmail, IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUserDto{
    @ApiProperty({
        description: 'User\'s first name',
        example: '<PERSON>',
        minLength: 1,
        maxLength: 50,
    })
    @IsNotEmpty()
    @IsString()
    firstName:string;

    @ApiProperty({
        description: 'User\'s last name',
        example: 'Doe',
        minLength: 1,
        maxLength: 50,
    })
    @IsString()
    @IsNotEmpty()
    lastName:string;

    @ApiProperty({
        description: 'User\'s email address',
        example: '<EMAIL>',
        format: 'email',
    })
    @IsNotEmpty()
    @IsString()
    @IsEmail()
    email:string;

    @ApiProperty({
        description: 'User\'s password',
        example: 'SecurePassword123!',
        minLength: 6,
        format: 'password',
    })
    @IsNotEmpty()
    @IsString()
    password:string;

    @ApiProperty({
        description: 'User\'s mobile phone number',
        example: '+1234567890',
        pattern: '^[+]?[1-9]\\d{1,14}$',
    })
    @IsNotEmpty()
    @IsString()
    mobileNumber:string;

    @ApiProperty({
        description: 'User\'s gender',
        example: 'Male',
        enum: ['Male', 'Female', 'Other'],
    })
    @IsNotEmpty()
    @IsString()
    gender:string;

    @ApiProperty({
        description: 'User\'s date of birth',
        example: '1990-01-15',
        format: 'date',
    })
    @IsNotEmpty()
    @IsString()
    dob:string;
}