export interface IUser {
 _id: string;
 firstName: string;
 lastName: string;
 email: string;
 password: string;
 mobileNumber: string;
 gender: string;
 dob: string;
 isVerified?: boolean;
 healthAssessment?: IHealthAssessment;
  diabetesInfo?: IDiabetesInfo;
  bloodPressureInfo?: IBloodPressureInfo;
  mentalHealthInfo?: IMentalHealthInfo;
  sleepInfo?: ISleepInfo;
  fitnessInfo?: IFitnessInfo;
  Reminders?: IMeasurementReminders;
}

export interface IHealthAssessment {
  ageRange?: string;
  height?: number;
  weight?: number;
  bmi?: number;
  bodyFatPercentage?: number;
  conditionsToManage?: string[];
}

export interface IDiabetesInfo {
  diabetesType?: string;
  diabetesDiagnosedSince?: string;
  takesMedication?: boolean;
  medications?: string[];
  recentHbA1c?: number;
  rememberHbA1c?: boolean;
  lastTestDuration?: string;
  recentFastingGlucose?: number;
  diabetesMotivationLevel?: number;
}

export interface IBloodPressureInfo {
  hyperTension?: boolean;
  diagonisedYear?: number;
  bpSystolic?: number;
  bpDiastolic?: number;
  heartRate?: number;
  rememberBp?: boolean;
}

export interface IMeasurementReminders {
  bpMeasurementReminders?: boolean;
  bloodSugarMeasurementReminders?: boolean;
}

export interface IMentalHealthInfo {
  comorbidities?: string[];
  anxietyLevel?: string;
  commonEmotions?: string[];
}

export interface ISleepInfo {
  sleepQualityRating?: string;
  troubleFallingAsleep?: string;
  sleepCondition?: string;
}

export interface IFitnessInfo {
  fitnessLevel?: string;
  motivationInsightsAccepted?: boolean;
}

